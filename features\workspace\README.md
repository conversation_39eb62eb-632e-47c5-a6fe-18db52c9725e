# 📝 Workspace Documents Module

## Overview

The Workspace Documents module provides a comprehensive rich text editing environment with advanced features for document creation, editing, and management. Built on top of Quill.js, it offers a modern, feature-rich editor with support for multimedia content, task attachments, and collaborative features.

## 🏗️ Module Structure

```
features/workspace/
├── core/                           # Core functionality
│   ├── workspace-core.js          # Main editor initialization and setup
│   └── workspace-document.js      # Document operations (save, load, export)
├── formatting/                     # Text formatting features
│   └── workspace-formatting.js   # Rich text formatting controls
├── media/                         # Media handling
│   ├── workspace-media.js        # Image and media insertion
│   └── workspace-tables-links.js # Table and link management
├── attachments/                   # File attachments
│   └── workspace-attachments.js  # Task attachment system
├── ui/                           # User interface
│   ├── workspace-ui.js           # UI utilities and theme management
│   ├── workspace.html            # Main workspace interface
│   └── workspace.css             # Styling and themes
├── test-workspace.html           # Module testing interface
└── README.md                     # This documentation
```

## ✨ Features

### Rich Text Editing
- **Quill.js Integration**: Professional-grade rich text editor
- **Formatting Tools**: Bold, italic, underline, strikethrough, colors
- **Typography**: Font family and size selection
- **Alignment**: Left, center, right, justify text alignment
- **Lists**: Bullet points and numbered lists

### Media Support
- **Image Insertion**: Upload from computer, Google Drive, or URL
- **Drag & Drop**: Direct file dropping into editor
- **Table Creation**: Dynamic table insertion with customizable rows/columns
- **Link Management**: Easy hyperlink insertion and editing

### Document Operations
- **Auto-save**: Automatic content saving every 30 seconds
- **Export Options**: PDF and Word document export
- **Import/Export**: Document import and export functionality
- **Version Control**: Undo/redo with extensive history

### Speech Features
- **Speech Recognition**: Live lecture recording and transcription
- **Text-to-Speech**: Read selected text or entire document
- **Language Support**: Multiple language recognition
- **Voice Controls**: Pause, resume, and stop functionality

### Task Integration
- **Attachment System**: View and manage task-related files
- **Google Drive Integration**: Direct access to cloud files
- **Flashcard Integration**: Embedded flashcard system
- **Task Selector**: Easy switching between task contexts

### UI/UX Features
- **Dark/Light Theme**: Toggle between themes with persistence
- **Floating Toolbar**: Context-sensitive formatting tools
- **Zoom Controls**: Adjustable editor zoom levels
- **Status Tracking**: Word count, character count, save status
- **Responsive Design**: Mobile and desktop compatibility

## 🚀 Usage

### Basic Setup

1. **Include the module in your HTML:**
```html
<link rel="stylesheet" href="features/workspace/ui/workspace.css">
<script src="features/workspace/ui/workspace-ui.js"></script>
<script src="features/workspace/formatting/workspace-formatting.js"></script>
<script src="features/workspace/core/workspace-document.js"></script>
<script src="features/workspace/media/workspace-media.js"></script>
<script src="features/workspace/media/workspace-tables-links.js"></script>
<script src="features/workspace/attachments/workspace-attachments.js"></script>
<script src="features/workspace/core/workspace-core.js"></script>
```

2. **Initialize the workspace:**
```javascript
// The workspace initializes automatically when the DOM is loaded
// Access the global quill instance: window.quill
```

### API Usage

#### Core Functions
```javascript
// Get editor content
const content = quill.getContents();

// Set editor content
quill.setContents(deltaContent);

// Get plain text
const text = quill.getText();

// Insert text at cursor
quill.insertText(quill.getSelection().index, 'Hello World');
```

#### Document Operations
```javascript
// Save document
saveDocument();

// Create new document
newDocument();

// Export as PDF
exportAsPDF();

// Export as Word
exportAsWord();
```

#### Formatting
```javascript
// Apply formatting
toggleFormat('bold');
toggleFormat('italic');

// Set font
updateFormat('fontFamily');
updateFormat('fontSize');
```

#### UI Controls
```javascript
// Show toast notification
showToast('Message', 'success', 3000);

// Toggle theme
toggleTheme();

// Update word count
updateCounts();
```

## 🔧 Configuration

### Theme Customization
The module supports extensive CSS custom properties for theming:

```css
:root {
    --primary-color: #4f46e5;
    --background-color: #111827;
    --text-color: #f9fafb;
    --card-bg: #374151;
    /* ... more variables */
}
```

### Quill Configuration
The editor can be customized by modifying the Quill initialization in `workspace-core.js`:

```javascript
window.quill = new Quill('#editor', {
    theme: 'snow',
    modules: {
        toolbar: false, // Custom toolbar
        history: {
            delay: 2000,
            maxStack: 500,
            userOnly: true
        }
    },
    formats: [
        'bold', 'italic', 'underline', 'strike',
        'align', 'list', 'bullet', 'indent',
        'link', 'image', 'video',
        'color', 'background',
        'font', 'size', 'header',
        'blockquote', 'code-block',
        'table'
    ]
});
```

## 🔗 Dependencies

### External Libraries
- **Quill.js** (1.3.6): Rich text editor
- **Bootstrap Icons**: Icon library
- **PDF.js**: PDF preview functionality
- **html2pdf.js**: PDF export
- **docx.js**: Word document export

### Internal Dependencies
- `js/speech-recognition.js`: Speech recognition functionality
- `js/speech-synthesis.js`: Text-to-speech features
- `js/workspaceFlashcardIntegration.js`: Flashcard integration
- `js/cross-tab-sync.js`: Cross-tab synchronization
- `js/sm2.js`: Spaced repetition algorithm
- `js/inject-header.js`: Header injection

### Google APIs
- Google Drive API: File management
- Google Picker API: File selection
- Google Generative AI: Content analysis

## 🧪 Testing

Run the test suite by opening `test-workspace.html` in your browser:

```bash
# Open in browser
file:///path/to/features/workspace/test-workspace.html
```

The test suite verifies:
- Module file existence
- HTML/CSS loading
- JavaScript module integrity
- Live workspace functionality

## 🐛 Known Issues

1. **PDF Preview**: Some PDF files may not load correctly with PDF.js fallback
2. **Speech Recognition**: Requires HTTPS for production use
3. **Google Drive**: Requires proper API key configuration
4. **Mobile Support**: Some features may be limited on mobile devices

## 🔄 Integration Points

### Task Management
- Integrates with task management system for attachments
- Supports task-specific document contexts
- Flashcard integration for study materials

### Storage Systems
- Local storage for auto-save
- Google Drive for cloud storage
- Cross-tab synchronization support

### AI Features
- Speech recognition for transcription
- Text-to-speech for accessibility
- AI-powered content analysis (when configured)

## 📈 Performance

- **Auto-save**: Every 30 seconds to prevent data loss
- **Lazy Loading**: PDF.js loads only when needed
- **Optimized Rendering**: Efficient Quill.js rendering
- **Memory Management**: Proper cleanup of event listeners

## 🤝 Contributing

When modifying this module:

1. Maintain the modular structure
2. Update this README for new features
3. Test with `test-workspace.html`
4. Ensure cross-browser compatibility
5. Follow the existing code style

## 📄 License

This module is part of the GPAce application and follows the same licensing terms.
