/* Flashcards CSS */

/* Deck cards styling */
.deck-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.deck-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.deck-card .card-body {
    display: flex;
    flex-direction: column;
}

.deck-card .card-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.deck-card .card-text {
    color: var(--text-secondary);
    flex-grow: 1;
}

.deck-card .card-footer {
    background-color: transparent;
    border-top: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
}

.deck-card .badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
}

.deck-card .deck-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.deck-card:hover .deck-actions {
    opacity: 1;
}

/* Subject header styling */
.subject-header {
    color: var(--primary-color);
    font-weight: 600;
    margin-top: 1rem;
    padding-left: 0.5rem;
    border-left: 4px solid var(--primary-color);
}

/* Sub-decks styling */
.sub-decks-container {
    background-color: var(--surface-color, #1f2937);
    border-radius: 10px;
    padding: 15px;
    margin-top: -10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.sub-decks-header {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 10px;
    font-weight: 500;
}

.sub-deck-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    height: 100%;
    border-radius: 8px;
    background-color: var(--card-bg, #374151);
    border: 1px solid var(--border-color);
}

.sub-deck-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.sub-deck-card .card-body {
    padding: 0.75rem;
}

.sub-deck-card .card-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.has-sub-decks {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* Light theme adjustments */
body.light-theme .sub-decks-container {
    background-color: #f8f9fa;
}

body.light-theme .sub-deck-card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
}

/* Flashcard styling */
.flashcard-container {
    perspective: 1000px;
    width: 100%;
    height: 300px;
}

.flashcard {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.6s;
}

.flashcard.flipped {
    transform: rotateY(180deg);
}

.flashcard-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flashcard-front, .flashcard-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.flashcard-front {
    background-color: var(--card-bg, #374151);
    color: var(--text-color, #f9fafb);
}

.flashcard-back {
    background-color: var(--surface-color, #1f2937);
    color: var(--text-color, #f9fafb);
    transform: rotateY(180deg);
}

.flashcard-content {
    max-width: 100%;
    max-height: 100%;
    overflow: auto;
    padding: 10px;
}

/* Rating buttons */
.rating-btn {
    transition: all 0.2s ease;
}

.rating-btn:hover {
    transform: scale(1.05);
}

/* Deck details */
.deck-info {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .rating-container .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    .rating-container .bi {
        margin-right: 0;
    }

    .rating-btn span {
        display: none;
    }

    .flashcard-container {
        height: 250px;
    }
}

/* Light theme adjustments */
body.light-theme .flashcard-front {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #e0e0e0;
}

body.light-theme .flashcard-back {
    background-color: #f8f9fa;
    color: #333333;
    border: 1px solid #e0e0e0;
}

/* Animation for new cards */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.new-card-animation {
    animation: fadeIn 0.5s ease forwards;
}

/* Progress bar styling */
.progress-container {
    height: 5px;
    width: 100%;
    background-color: var(--border-color);
    margin-bottom: 1rem;
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 3rem;
    color: var(--text-tertiary);
    margin-bottom: 1rem;
}

.empty-state p {
    color: var(--text-secondary);
    max-width: 300px;
    margin: 0 auto;
}
