# 🧩 Modular Plan (Live Coordination)

This file contains the proposed modular breakdown and live tracking of agent tasks. Agents must update their section as they complete work to prevent redundancy.

---

## 🗂 Proposed Structure

```
features/
├── flashcards/                 # Spaced repetition flashcard system
│   ├── core/                   # Core flashcard logic
│   ├── ui/                     # Flashcard UI components
│   └── integration/            # Workspace integration
├── workspace/                  # Rich text editor and document management
│   ├── core/                   # Core workspace functionality
│   ├── formatting/             # Text formatting features
│   ├── media/                  # Media handling
│   └── attachments/            # File attachments
├── priority-calculator/        # Task priority calculation system
│   ├── core/                   # Priority calculation logic
│   ├── workers/                # Web workers for calculations
│   └── ui/                     # Priority display components
├── tasks/                      # Task management system
│   ├── core/                   # Task CRUD operations
│   ├── filters/                # Task filtering and search
│   ├── notes/                  # Task notes functionality
│   └── links/                  # Task linking system
├── calendar/                   # Calendar and scheduling
│   ├── daily/                  # Daily calendar view
│   ├── timetable/              # Timetable management
│   └── scheduling/             # Schedule optimization
├── grind-mode/                 # Pomodoro timer and focus mode
│   ├── timer/                  # Timer functionality
│   ├── ai-research/            # AI-powered research
│   └── simulation/             # Study simulation
├── study-spaces/               # Study location management
│   ├── core/                   # Space management
│   ├── analyzer/               # Space analysis
│   └── firestore/              # Cloud storage
├── sleep-management/           # Sleep tracking and alarms
│   ├── alarms/                 # Alarm system
│   ├── schedule/               # Sleep schedule
│   └── saboteurs/              # Sleep disruption tracking
├── academic-details/           # Academic information management
│   ├── subjects/               # Subject management
│   ├── marks/                  # Grade tracking
│   └── semester/               # Semester system
├── ai-integration/             # AI-powered features
│   ├── gemini/                 # Google Gemini API
│   ├── research/               # AI research capabilities
│   └── latex/                  # LaTeX conversion
└── test-feedback/              # Instant test feedback system

services/
├── firebase/                   # Firebase services
│   ├── auth/                   # Authentication
│   ├── firestore/              # Database operations
│   └── config/                 # Firebase configuration
├── api/                        # External API integrations
│   ├── todoist/                # Todoist integration
│   ├── google-drive/           # Google Drive API
│   └── gemini/                 # Gemini API service
├── storage/                    # Data storage services
│   ├── local/                  # Local storage management
│   ├── indexed-db/             # IndexedDB operations
│   └── sync/                   # Cross-tab synchronization
├── audio/                      # Audio services
│   ├── sound-manager/          # Sound effects
│   ├── speech-recognition/     # Voice input
│   └── speech-synthesis/       # Text-to-speech
└── data-sync/                  # Data synchronization

utils/
├── ui/                         # UI utilities
│   ├── theme-manager/          # Theme switching
│   ├── side-drawer/            # Navigation drawer
│   ├── notifications/          # Toast notifications
│   └── transitions/            # Page transitions
├── algorithms/                 # Algorithmic utilities
│   ├── sm2/                    # Spaced repetition algorithm
│   ├── priority/               # Priority calculation helpers
│   └── time/                   # Time calculation utilities
├── formatters/                 # Data formatting
│   ├── markdown/               # Markdown processing
│   ├── pandoc/                 # Pandoc fallback
│   └── text-expansion/         # Text expansion
└── helpers/                    # General helpers
    ├── common/                 # Common utilities
    ├── validation/             # Input validation
    └── file-handling/          # File operations

components/
├── shared/                     # Shared UI components
│   ├── header/                 # Common header
│   ├── navigation/             # Navigation components
│   └── modals/                 # Modal dialogs
├── forms/                      # Form components
└── charts/                     # Data visualization

assets/
├── images/                     # Image assets
├── audio/                      # Audio files
├── fonts/                      # Custom fonts
└── icons/                      # Icon assets

config/
├── firebase/                   # Firebase configuration
├── api/                        # API configurations
└── app/                        # Application settings

docs/
├── features/                   # Feature documentation
├── api/                        # API documentation
└── deployment/                 # Deployment guides

experiments/
├── crush-assignment/           # Markdown to PDF converter
├── youtube-searcher/           # YouTube search experiment
└── relaxed-mode/               # Relaxed mode experiment

pending-review/                 # Files needing classification
```

---

## 👨‍💻 Agent Assignments

| Agent ID | Feature | Folder | Tasks |
|----------|---------|--------|-------|
| agent-001 | Flashcards System | features/flashcards/ | Move flashcard files, organize by core/ui/integration, fix imports, document API |
| agent-002 | Workspace & Documents | features/workspace/ | Refactor workspace files, organize by functionality, test editor integration |
| agent-003 | Priority Calculator | features/priority-calculator/ | Move priority calculation files, organize workers, fix worker paths |
| agent-004 | Tasks Management | features/tasks/ | Organize task-related files, separate core logic from UI, fix integrations |
| agent-005 | Calendar & Scheduling | features/calendar/ | Move calendar files, organize daily/timetable views, fix date handling |
| agent-006 | Grind Mode & Timer | features/grind-mode/ | Organize pomodoro timer, AI research, simulation features |
| agent-007 | Study Spaces | features/study-spaces/ | Move study space files, organize core/analyzer/firestore modules |
| agent-008 | Sleep Management | features/sleep-management/ | Organize alarm system, sleep schedule, saboteurs tracking |
| agent-009 | Academic Details | features/academic-details/ | Move academic files, organize subjects/marks/semester modules |
| agent-010 | AI Integration | features/ai-integration/ | Organize AI-related files, separate Gemini/research/LaTeX |
| agent-011 | Firebase Services | services/firebase/ | Move Firebase files, organize auth/firestore/config |
| agent-012 | API Services | services/api/ | Organize external API integrations, separate by service |
| agent-013 | Storage Services | services/storage/ | Move storage-related files, organize local/indexed-db/sync |
| agent-014 | Audio Services | services/audio/ | Organize audio files, separate sound/speech features |
| agent-015 | UI Utils & Components | utils/ui/ + components/ | Move UI utilities and shared components |
| agent-016 | Core Utils & Algorithms | utils/algorithms/ + utils/helpers/ | Organize algorithmic and helper utilities |
| agent-017 | Assets & Config | assets/ + config/ | Consolidate assets, organize configuration files |
| agent-018 | Documentation & Cleanup | docs/ + experiments/ + pending-review/ | Organize documentation, move experiments, classify pending files |

---

## 📝 Agent Logs

### agent-001 (Flashcards System)
🟡 Awaiting approval to begin modular refactoring

### agent-002 (Workspace & Documents)
🟡 Awaiting approval to begin modular refactoring

### agent-003 (Priority Calculator)
🟡 Awaiting approval to begin modular refactoring

### agent-004 (Tasks Management)
🟡 Awaiting approval to begin modular refactoring

### agent-005 (Calendar & Scheduling)
🟡 Awaiting approval to begin modular refactoring

### agent-006 (Grind Mode & Timer)
🟡 Awaiting approval to begin modular refactoring

### agent-007 (Study Spaces)
🟡 Awaiting approval to begin modular refactoring

### agent-008 (Sleep Management)
🟡 Awaiting approval to begin modular refactoring

### agent-009 (Academic Details)
🟡 Awaiting approval to begin modular refactoring

### agent-010 (AI Integration)
🟡 Awaiting approval to begin modular refactoring

### agent-011 (Firebase Services)
🟡 Awaiting approval to begin modular refactoring

### agent-012 (API Services)
🟡 Awaiting approval to begin modular refactoring

### agent-013 (Storage Services)
🟡 Awaiting approval to begin modular refactoring

### agent-014 (Audio Services)
🟡 Awaiting approval to begin modular refactoring

### agent-015 (UI Utils & Components)
🟡 Awaiting approval to begin modular refactoring

### agent-016 (Core Utils & Algorithms)
🟡 Awaiting approval to begin modular refactoring

### agent-017 (Assets & Config)
🟡 Awaiting approval to begin modular refactoring

### agent-018 (Documentation & Cleanup)
🟡 Awaiting approval to begin modular refactoring

---

## 🎯 Coordination Rules

1. **Live Updates**: Agents must update their log section every 15 minutes with progress
2. **Dependency Management**: Check for file dependencies before moving
3. **Import Path Updates**: Update all relative imports when moving files
4. **Testing**: Test functionality after each major move
5. **Documentation**: Create README.md for each feature module
6. **Conflict Resolution**: Tag @supervisor for any blocking issues

---

## 📊 Progress Tracking

- **Total Features**: 10 major feature areas
- **Total Services**: 4 service categories  
- **Total Utils**: 4 utility categories
- **Estimated Completion**: 4-6 hours with 18 agents
- **Current Phase**: Planning & Approval

---

*Last Updated: Awaiting human approval to begin modular refactoring*
