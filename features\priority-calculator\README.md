# Priority Calculator Feature

## Overview

The Priority Calculator is a sophisticated task prioritization system that uses multiple factors to calculate priority scores for academic tasks. It helps students focus on the most important tasks based on credit hours, cognitive difficulty, task weightage, deadlines, and academic performance.

## Features

- **Multi-factor Priority Calculation**: Combines credit hours, cognitive difficulty, task weightage, time remaining, and academic performance
- **Web Worker Support**: Offloads CPU-intensive calculations to worker threads for better performance
- **Real-time Updates**: Automatically recalculates priorities when tasks or academic data changes
- **Cross-tab Synchronization**: Keeps priority data synchronized across browser tabs
- **Firestore Integration**: Saves calculated priorities to cloud storage
- **Responsive UI**: Clean, mobile-friendly interface with detailed score breakdowns

## Directory Structure

```
features/priority-calculator/
├── core/                           # Core calculation logic
│   ├── priority-calculator.js      # Main priority calculation functions
│   └── priority-calculator-with-worker.js  # Worker-enhanced version
├── workers/                        # Web worker files
│   ├── worker.js                   # Main calculation worker
│   ├── test-worker.js              # Worker testing script
│   └── priority-worker-wrapper.js  # Worker wrapper and interface
├── ui/                            # User interface files
│   ├── priority-calculator.html   # Main calculator page
│   ├── priority-list.html         # Priority list view
│   ├── priority-calculator.css    # Calculator styles
│   └── priority-list.css          # List view styles
└── README.md                      # This documentation
```

## Priority Calculation Formula

The priority score is calculated using the following formula:

```
Priority Score = (CHP + CDP + TWP + TRP) × (1 - APA/100)
```

Where:
- **CHP (Credit Hours Points)**: Relative importance based on subject credit hours (0-100)
- **CDP (Cognitive Difficulty Points)**: Complexity of the subject (1-100)
- **TWP (Task Weightage Points)**: Importance within the task section (0-100)
- **TRP (Time Remaining Points)**: Urgency based on deadline
- **APA (Academic Performance Adjustment)**: Performance-based reduction (0-100%)

### Time Remaining Points Calculation

- **Upcoming Tasks**: `TRP = 10 × (1 / (Days + Hours/24))`
- **Overdue Tasks**: `TRP = 10 × (1 + log(Overdue Days + 1))`

## API Reference

### Core Functions

#### `calculateAllTasksPriorities()`
Calculates priority scores for all tasks across all subjects.
- **Returns**: Array of tasks with priority scores
- **Side Effects**: Updates localStorage and broadcasts changes

#### `calculateTaskScore(task, creditHoursPoints, cognitiveDifficultyPoints, projectId)`
Calculates the priority score for a single task.
- **Parameters**:
  - `task`: Task object with title, section, dueDate
  - `creditHoursPoints`: Credit hours points for the subject
  - `cognitiveDifficultyPoints`: Cognitive difficulty points
  - `projectId`: Subject/project identifier
- **Returns**: Calculated priority score

#### `updatePriorityScores()`
Updates the UI display with recalculated priority scores.
- **Side Effects**: Refreshes the priority list display

### Worker Functions

#### `calculatePrioritiesWithWorker()`
Calculates priorities using a web worker for better performance.
- **Returns**: Promise resolving to array of prioritized tasks
- **Fallback**: Falls back to main thread calculation if worker fails

### Utility Functions

#### `calculateTimeRemainingPoints(dueDate)`
Calculates urgency points based on task deadline.
- **Parameters**: `dueDate` - Task due date string
- **Returns**: Time-based priority points

#### `calculateTaskWeightage(projectId, taskSection)`
Gets the weightage for a specific task section.
- **Parameters**:
  - `projectId`: Subject identifier
  - `taskSection`: Task section (assignment, quiz, midterm, final, etc.)
- **Returns**: Weightage points for the section

## Dependencies

### External Dependencies
- Bootstrap 5.3.2 (UI framework)
- Bootstrap Icons (iconography)
- Firebase SDK (cloud storage)

### Internal Dependencies
- `js/common.js` - Common utility functions
- `js/cross-tab-sync.js` - Cross-tab synchronization
- `js/sideDrawer.js` - Navigation drawer
- `js/firebaseConfig.js` - Firebase configuration
- `js/auth.js` - Authentication functions
- `js/initFirestoreData.js` - Firestore initialization

## Usage

### Basic Usage

1. **Include the core script**:
   ```html
   <script src="../core/priority-calculator.js"></script>
   ```

2. **Initialize priority calculation**:
   ```javascript
   // Calculate priorities for all tasks
   const prioritizedTasks = calculateAllTasksPriorities();
   
   // Update the UI display
   updatePriorityScores();
   ```

### Worker-Enhanced Usage

1. **Include the worker-enhanced script**:
   ```html
   <script src="../core/priority-calculator-with-worker.js"></script>
   ```

2. **Use worker-based calculation**:
   ```javascript
   // Calculate priorities using web worker
   const prioritizedTasks = await calculatePrioritiesWithWorker();
   ```

### Testing Workers

Run the worker test script to verify functionality:
```bash
node features/priority-calculator/workers/test-worker.js
```

## Data Storage

### LocalStorage Keys
- `academicSubjects` - Array of academic subjects
- `tasks-{projectId}` - Tasks for specific subjects
- `subjectMarks` - Academic performance data
- `subjectWeightages` - Task weightages by subject
- `projectWeightages` - Legacy project weightages
- `calculatedPriorityTasks` - Calculated priority results

### Firestore Collections
- `users/{userId}/settings/priorityTasks` - Cloud-stored priority calculations

## Known Issues

1. **Worker Compatibility**: Web workers require HTTPS or localhost for security
2. **Date Parsing**: Some date formats may not parse correctly
3. **Performance**: Large numbers of tasks may impact calculation speed

## Future Enhancements

- [ ] Machine learning-based priority prediction
- [ ] Integration with calendar systems
- [ ] Advanced filtering and sorting options
- [ ] Priority trend analysis and reporting
- [ ] Mobile app integration

## Contributing

When modifying the priority calculator:

1. Update both regular and worker versions of calculation functions
2. Test worker functionality with the test script
3. Update this README if adding new features
4. Ensure cross-tab sync compatibility
5. Test with various task loads and deadline scenarios
