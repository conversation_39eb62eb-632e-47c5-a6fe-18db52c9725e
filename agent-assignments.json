{"agent-001": {"feature": "flashcards-system", "folder": "features/flashcards/", "description": "Spaced repetition flashcard system with SM-2 algorithm", "files_to_move": ["js/flashcards.js", "js/flashcardManager.js", "js/flashcardTaskIntegration.js", "js/workspaceFlashcardIntegration.js", "js/sm2.js", "html/flashcards.html", "css/flashcards.css"], "target_structure": {"core/": ["flashcards.js", "flashcardManager.js", "sm2.js"], "ui/": ["flashcards.html", "flashcards.css"], "integration/": ["flashcardTaskIntegration.js", "workspaceFlashcardIntegration.js"]}, "tasks": ["Create features/flashcards/ directory structure", "Move flashcard-related JS, HTML, and CSS files", "Organize files into core/ui/integration subdirectories", "Fix broken imports and relative paths", "Ensure self-contained module structure", "Write README.md for feature usage and API calls", "Test flashcard creation and study functionality", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-002": {"feature": "workspace-documents", "folder": "features/workspace/", "description": "Rich text editor and document management system", "files_to_move": ["js/workspace-core.js", "js/workspace-ui.js", "js/workspace-formatting.js", "js/workspace-document.js", "js/workspace-media.js", "js/workspace-tables-links.js", "js/workspace-attachments.js", "html/workspace.html", "css/workspace.css"], "target_structure": {"core/": ["workspace-core.js"], "formatting/": ["workspace-formatting.js"], "media/": ["workspace-media.js", "workspace-tables-links.js"], "attachments/": ["workspace-attachments.js"], "ui/": ["workspace-ui.js", "workspace.html", "workspace.css"]}, "tasks": ["Create features/workspace/ directory structure", "Move workspace-related files to appropriate subdirectories", "Fix Quill.js editor integration paths", "Update document export/import functionality", "Test rich text editing and media insertion", "Ensure attachment system works correctly", "Write comprehensive README.md", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-003": {"feature": "priority-calculator", "folder": "features/priority-calculator/", "description": "Task priority calculation system with web workers", "files_to_move": ["js/priority-calculator.js", "js/priority-calculator-with-worker.js", "js/priority-worker-wrapper.js", "js/workers/worker.js", "js/workers/test-worker.js", "html/priority-calculator.html", "html/priority-list.html", "css/priority-calculator.css", "css/priority-list.css"], "target_structure": {"core/": ["priority-calculator.js", "priority-calculator-with-worker.js"], "workers/": ["worker.js", "test-worker.js", "priority-worker-wrapper.js"], "ui/": ["priority-calculator.html", "priority-list.html", "priority-calculator.css", "priority-list.css"]}, "tasks": ["Create features/priority-calculator/ directory structure", "Move priority calculation files to appropriate subdirectories", "Fix web worker paths and imports", "Update worker wrapper references", "Test priority calculation algorithms", "Verify worker communication works", "Write README.md with calculation methodology", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-004": {"feature": "tasks-management", "folder": "features/tasks/", "description": "Task management system with filtering and notes", "files_to_move": ["js/tasksManager.js", "js/currentTaskManager.js", "js/taskFilters.js", "js/taskLinks.js", "js/taskAttachments.js", "js/task-notes.js", "js/task-notes-injector.js", "html/tasks.html", "css/tasks.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css"], "target_structure": {"core/": ["tasksManager.js", "currentTaskManager.js"], "filters/": ["taskFilters.js"], "notes/": ["task-notes.js", "task-notes-injector.js"], "links/": ["taskLinks.js", "taskAttachments.js"], "ui/": ["tasks.html", "tasks.css", "task-display.css", "task-notes.css", "taskLinks.css"]}, "tasks": ["Create features/tasks/ directory structure", "Move task-related files to appropriate subdirectories", "Fix Todoist integration imports", "Update task filtering and search functionality", "Test task CRUD operations", "Verify task notes and attachments work", "Write README.md with task management API", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-005": {"feature": "calendar-scheduling", "folder": "features/calendar/", "description": "Calendar and scheduling system with timetable management", "files_to_move": ["js/calendarManager.js", "js/calendar-views.js", "js/scheduleManager.js", "js/timetableAnalyzer.js", "js/timetableIntegration.js", "js/sleepScheduleManager.js", "js/sleepTimeCalculator.js", "html/daily-calendar.html", "css/daily-calendar.css", "css/calendar.css"], "target_structure": {"daily/": ["calendarManager.js", "calendar-views.js", "daily-calendar.html", "daily-calendar.css"], "timetable/": ["timetableAnalyzer.js", "timetableIntegration.js"], "scheduling/": ["scheduleManager.js", "sleepScheduleManager.js", "sleepTimeCalculator.js"], "ui/": ["calendar.css"]}, "tasks": ["Create features/calendar/ directory structure", "Move calendar and scheduling files to subdirectories", "Fix date handling and timezone issues", "Update timetable analysis functionality", "Test calendar event creation and editing", "Verify sleep schedule integration", "Write README.md with calendar API documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-006": {"feature": "grind-mode-timer", "folder": "features/grind-mode/", "description": "Pomodoro timer with AI research and study simulation", "files_to_move": ["js/pomodoroTimer.js", "js/pomodoroGlobal.js", "js/ai-researcher.js", "js/simulation-enhancer.js", "js/grind-speech-synthesis.js", "html/grind.html", "css/grind.css", "css/simulation-enhancer.css"], "target_structure": {"timer/": ["pomodoroTimer.js", "pomodoroGlobal.js"], "ai-research/": ["ai-researcher.js"], "simulation/": ["simulation-enhancer.js"], "ui/": ["grind.html", "grind.css", "simulation-enhancer.css", "grind-speech-synthesis.js"]}, "tasks": ["Create features/grind-mode/ directory structure", "Move pomodoro timer and AI research files", "Fix timer state management and persistence", "Update AI research integration with Gemini API", "Test pomodoro timer functionality", "Verify simulation enhancer works", "Write README.md with grind mode documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-007": {"feature": "study-spaces", "folder": "features/study-spaces/", "description": "Study location management with analysis and cloud storage", "files_to_move": ["js/studySpacesManager.js", "js/studySpaceAnalyzer.js", "js/studySpacesFirestore.js", "html/study-spaces.html", "css/study-spaces.css"], "target_structure": {"core/": ["studySpacesManager.js"], "analyzer/": ["studySpaceAnalyzer.js"], "firestore/": ["studySpacesFirestore.js"], "ui/": ["study-spaces.html", "study-spaces.css"]}, "tasks": ["Create features/study-spaces/ directory structure", "Move study space files to appropriate subdirectories", "Fix Firestore integration for cloud storage", "Update image upload and analysis functionality", "Test study space creation and management", "Verify location-based features work", "Write README.md with study spaces API", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-008": {"feature": "sleep-management", "folder": "features/sleep-management/", "description": "Sleep tracking, alarms, and disruption management", "files_to_move": ["js/alarm-service.js", "js/alarm-handler.js", "js/alarm-data-service.js", "js/alarm-mini-display.js", "js/alarm-service-worker.js", "js/sleep-saboteurs-init.js", "js/clock-display.js", "html/sleep-saboteurs.html", "css/alarm-service.css", "css/sleep-saboteurs.css"], "target_structure": {"alarms/": ["alarm-service.js", "alarm-handler.js", "alarm-data-service.js", "alarm-mini-display.js", "alarm-service-worker.js"], "schedule/": ["clock-display.js"], "saboteurs/": ["sleep-saboteurs-init.js"], "ui/": ["sleep-saboteurs.html", "alarm-service.css", "sleep-saboteurs.css"]}, "tasks": ["Create features/sleep-management/ directory structure", "Move alarm and sleep-related files to subdirectories", "Fix alarm service worker registration", "Update sleep schedule tracking functionality", "Test alarm creation and notification system", "Verify sleep saboteurs tracking works", "Write README.md with sleep management API", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-009": {"feature": "academic-details", "folder": "features/academic-details/", "description": "Academic information management with subjects and grades", "files_to_move": ["js/academic-details.js", "js/subject-management.js", "js/subject-marks.js", "js/subject-marks-ui.js", "js/subject-marks-integration.js", "js/semester-management.js", "js/marks-tracking.js", "html/academic-details.html", "html/subject-marks.html", "css/academic-details.css", "css/subject-marks.css"], "target_structure": {"subjects/": ["subject-management.js"], "marks/": ["subject-marks.js", "subject-marks-ui.js", "subject-marks-integration.js", "marks-tracking.js"], "semester/": ["semester-management.js"], "ui/": ["academic-details.js", "academic-details.html", "subject-marks.html", "academic-details.css", "subject-marks.css"]}, "tasks": ["Create features/academic-details/ directory structure", "Move academic and subject management files", "Fix subject marks calculation and tracking", "Update semester management functionality", "Test grade tracking and performance analysis", "Verify academic details integration", "Write README.md with academic management API", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-010": {"feature": "ai-integration", "folder": "features/ai-integration/", "description": "AI-powered features with Gemini API and LaTeX conversion", "files_to_move": ["js/gemini-api.js", "js/googleGenerativeAI.js", "js/ai-latex-conversion.js", "js/imageAnalyzer.js", "html/instant-test-feedback.html", "css/ai-search-response.css", "css/test-feedback.css"], "target_structure": {"gemini/": ["gemini-api.js", "googleGenerativeAI.js"], "research/": ["imageAnalyzer.js"], "latex/": ["ai-latex-conversion.js"], "ui/": ["instant-test-feedback.html", "ai-search-response.css", "test-feedback.css"]}, "tasks": ["Create features/ai-integration/ directory structure", "Move AI-related files to appropriate subdirectories", "Fix Gemini API integration and configuration", "Update LaTeX conversion functionality", "Test AI-powered research and analysis", "Verify image analysis capabilities", "Write README.md with AI integration documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-011": {"feature": "firebase-services", "folder": "services/firebase/", "description": "Firebase authentication, Firestore, and configuration services", "files_to_move": ["js/firebase-config.js", "js/firebase-init.js", "js/firebaseAuth.js", "js/firebaseConfig.js", "js/firestore.js", "js/firestore-global.js", "js/initFirestoreData.js"], "target_structure": {"auth/": ["firebaseAuth.js"], "firestore/": ["firestore.js", "firestore-global.js", "initFirestoreData.js"], "config/": ["firebase-config.js", "firebase-init.js", "firebaseConfig.js"]}, "tasks": ["Create services/firebase/ directory structure", "Move Firebase-related files to appropriate subdirectories", "Fix Firebase configuration and initialization", "Update Firestore database operations", "Test authentication flow", "Verify Firestore data operations work", "Write README.md with Firebase services documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-012": {"feature": "api-services", "folder": "services/api/", "description": "External API integrations for Todoist, Google Drive, and Gemini", "files_to_move": ["js/todoistIntegration.js", "js/googleDriveApi.js", "js/api-optimization.js", "js/api-settings.js", "js/apiSettingsManager.js"], "target_structure": {"todoist/": ["todoistIntegration.js"], "google-drive/": ["googleDriveApi.js"], "gemini/": ["api-optimization.js", "api-settings.js", "apiSettingsManager.js"]}, "tasks": ["Create services/api/ directory structure", "Move API integration files to subdirectories", "Fix Todoist API authentication and operations", "Update Google Drive API integration", "Test API rate limiting and optimization", "Verify API settings management works", "Write README.md with API services documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-013": {"feature": "storage-services", "folder": "services/storage/", "description": "Data storage services for local, IndexedDB, and synchronization", "files_to_move": ["js/storageManager.js", "js/indexedDB.js", "js/cross-tab-sync.js", "js/data-sync-manager.js", "js/data-sync-integration.js", "js/data-loader.js"], "target_structure": {"local/": ["storageManager.js"], "indexed-db/": ["indexedDB.js"], "sync/": ["cross-tab-sync.js", "data-sync-manager.js", "data-sync-integration.js", "data-loader.js"]}, "tasks": ["Create services/storage/ directory structure", "Move storage-related files to subdirectories", "Fix cross-tab synchronization functionality", "Update IndexedDB operations and management", "Test data persistence and retrieval", "Verify cross-tab sync works correctly", "Write README.md with storage services documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-014": {"feature": "audio-services", "folder": "services/audio/", "description": "Audio services for sound effects, speech recognition, and synthesis", "files_to_move": ["js/soundManager.js", "js/speech-recognition.js", "js/speech-synthesis.js"], "target_structure": {"sound-manager/": ["soundManager.js"], "speech-recognition/": ["speech-recognition.js"], "speech-synthesis/": ["speech-synthesis.js"]}, "tasks": ["Create services/audio/ directory structure", "Move audio-related files to subdirectories", "Fix sound effect management and playback", "Update speech recognition functionality", "Test text-to-speech synthesis", "Verify audio permissions and browser compatibility", "Write README.md with audio services documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-015": {"feature": "ui-utils-components", "folder": "utils/ui/ + components/", "description": "UI utilities and shared components for theme, navigation, and notifications", "files_to_move": ["js/theme-manager.js", "js/themeManager.js", "js/sideDrawer.js", "js/transitionManager.js", "js/ui-utilities.js", "js/common-header.js", "js/inject-header.js", "js/notification.js", "css/sideDrawer.css", "css/notification.css"], "target_structure": {"utils/ui/theme-manager/": ["theme-manager.js", "themeManager.js"], "utils/ui/side-drawer/": ["sideDrawer.js", "sideDrawer.css"], "utils/ui/transitions/": ["transitionManager.js"], "utils/ui/notifications/": ["notification.js", "notification.css"], "components/shared/header/": ["common-header.js", "inject-header.js"], "components/shared/navigation/": ["ui-utilities.js"]}, "tasks": ["Create utils/ui/ and components/ directory structures", "Move UI utility files to appropriate subdirectories", "Fix theme switching and persistence", "Update side drawer navigation functionality", "Test notification system and transitions", "Verify header injection works across pages", "Write README.md for UI utilities and components", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-016": {"feature": "core-utils-algorithms", "folder": "utils/algorithms/ + utils/helpers/", "description": "Core utilities and algorithms for calculations and data processing", "files_to_move": ["js/common.js", "js/text-expansion.js", "js/markdown-converter.js", "js/pandoc-fallback.js", "js/fileViewer.js", "js/userGuidance.js", "js/weightage-connector.js", "js/priority-list-sorting.js", "js/priority-list-utils.js", "js/priority-sync-fix.js", "js/energyLevels.js", "js/energyHologram.js", "js/quoteManager.js", "js/recipeManager.js", "js/roleModelManager.js"], "target_structure": {"utils/algorithms/priority/": ["priority-list-sorting.js", "priority-list-utils.js", "priority-sync-fix.js"], "utils/algorithms/time/": ["energyLevels.js", "energyHologram.js"], "utils/formatters/markdown/": ["markdown-converter.js", "pandoc-fallback.js"], "utils/formatters/text-expansion/": ["text-expansion.js"], "utils/helpers/common/": ["common.js", "userGuidance.js"], "utils/helpers/file-handling/": ["fileViewer.js"], "utils/helpers/validation/": ["weightage-connector.js"], "utils/helpers/content/": ["quoteManager.js", "recipeManager.js", "roleModelManager.js"]}, "tasks": ["Create utils/algorithms/ and utils/helpers/ directory structures", "Move utility files to appropriate subdirectories", "Fix common utility functions and dependencies", "Update text expansion and markdown processing", "Test file viewing and content management", "Verify energy tracking and priority algorithms", "Write README.md for core utilities documentation", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-017": {"feature": "assets-config", "folder": "assets/ + config/", "description": "Asset consolidation and configuration management", "files_to_move": ["firebase.json", "data/locations.json", "data/schedule.json", "data/timetable.json"], "target_structure": {"config/firebase/": ["firebase.json"], "config/app/": ["data/locations.json", "data/schedule.json", "data/timetable.json"]}, "tasks": ["Create assets/ and config/ directory structures", "Move configuration files to appropriate locations", "Organize data files into config/app/", "Verify asset paths are correctly updated", "Test configuration loading and validation", "Ensure Firebase configuration works", "Write README.md for assets and configuration", "Live update MODULAR_PLAN.md with progress logs"]}, "agent-018": {"feature": "documentation-cleanup", "folder": "docs/ + experiments/ + pending-review/", "description": "Documentation organization and experimental code cleanup", "files_to_move": ["README.md", "plan.md", "cross-tab-sync-standardization.md", "sideDrawer-standardization-documentation.md", "task-links-fix-documentation.md", "task-notes-improvements-2023-07-10-1530.md", "priority-worker-README.md", "docs/text-expansion.md", "Crush Assignment/", "Youtube Searcher (Not Completed)/", "relaxed-mode/", "end.txt", "filelist.txt"], "target_structure": {"docs/features/": ["cross-tab-sync-standardization.md", "sideDrawer-standardization-documentation.md", "task-links-fix-documentation.md", "task-notes-improvements-2023-07-10-1530.md", "priority-worker-README.md", "docs/text-expansion.md"], "docs/deployment/": ["README.md", "plan.md"], "experiments/crush-assignment/": ["Crush Assignment/"], "experiments/youtube-searcher/": ["Youtube Searcher (Not Completed)/"], "experiments/relaxed-mode/": ["relaxed-mode/"], "pending-review/": ["end.txt", "filelist.txt"]}, "tasks": ["Create docs/, experiments/, and pending-review/ directory structures", "Move documentation files to appropriate locations", "Organize experimental projects into experiments/", "Review and classify pending files", "Update documentation with new modular structure", "Create comprehensive project documentation", "Write master README.md for the modular architecture", "Live update MODULAR_PLAN.md with final completion status"]}}