# 🧠 Flashcards System

A comprehensive spaced repetition flashcard system implementing the SM-2 algorithm for optimal learning retention.

## 📁 Structure

```
features/flashcards/
├── core/                    # Core flashcard logic
│   ├── flashcards.js       # Main flashcard system
│   ├── flashcardManager.js # Flashcard management
│   └── sm2.js              # SM-2 spaced repetition algorithm
├── ui/                     # User interface components
│   ├── flashcards.html     # Main flashcard interface
│   └── flashcards.css      # Flashcard styling
├── integration/            # Integration modules
│   ├── flashcardTaskIntegration.js      # Task integration
│   └── workspaceFlashcardIntegration.js # Workspace integration
└── README.md              # This file
```

## 🚀 Features

### Core Functionality
- **Spaced Repetition**: Implements SM-2 algorithm for optimal review scheduling
- **Deck Management**: Create and organize flashcard decks by subject
- **Sub-decks**: Categorize cards by type (Revision, Assignment, Quizzes, etc.)
- **Firebase Integration**: Cloud storage and synchronization
- **Study Sessions**: Interactive study mode with rating system

### Integration Features
- **Task Integration**: Connect flashcards to specific tasks
- **Workspace Integration**: Embed flashcards in workspace documents
- **Cross-tab Sync**: Real-time synchronization across browser tabs

## 🔧 API Reference

### Core Classes

#### `SM2`
Implements the SuperMemo 2 spaced repetition algorithm.

```javascript
// Process a card rating
const updatedCard = SM2.processCard(card, quality);

// Parameters:
// - card: Flashcard object with sm2 properties
// - quality: Rating from 1-5 (1=blackout, 5=perfect)
```

#### `FlashcardTaskIntegration`
Integrates flashcards with the task management system.

```javascript
// Initialize integration
await flashcardTaskIntegration.init();

// Connect task to flashcard deck
connectTaskToFlashcardDeck(taskId, deckId, taskData);

// Find sub-deck for task
const deckId = await findSubDeckForTask(subjectTag, section);
```

#### `WorkspaceFlashcardIntegration`
Integrates flashcards with workspace documents.

```javascript
// Initialize integration
await workspaceFlashcardIntegration.init(workspaceAttachments);

// Render flashcards in workspace
await workspaceFlashcardIntegration.renderFlashcards(taskId, container);
```

### Key Functions

#### Deck Management
```javascript
// Create a new deck
await createDeck(title, subjectId, description);

// Load all decks
await loadDecks();

// Delete a deck
await deleteDeck(deckId);
```

#### Card Management
```javascript
// Add a card to deck
await addCard(deckId, question, answer);

// Update card
await updateCard(cardId, updates);

// Rate card during study
await rateCard(rating);
```

#### Study Sessions
```javascript
// Start study session
await startStudySession(deckId);

// Get due cards
const dueCards = await getDueCards(deckId);

// Process card rating
const updatedCard = processCard(card, rating);
```

## 🎯 Usage Examples

### Basic Deck Creation
```javascript
// Create a new flashcard deck
const deckId = await createDeck(
    "Biology Chapter 1", 
    "biology", 
    "Cell structure and function"
);

// Add cards to the deck
await addCard(deckId, "What is mitosis?", "Cell division process...");
```

### Study Session
```javascript
// Start studying a deck
await startStudySession(deckId);

// Rate a card (1-5 scale)
await rateCard(4); // Good recall
```

### Task Integration
```javascript
// Connect a task to flashcards
connectTaskToFlashcardDeck(
    "task-123", 
    "deck-456", 
    { subject: "biology", section: "revision" }
);
```

## 🔗 Dependencies

### External Libraries
- **Firebase**: Cloud storage and authentication
- **Bootstrap**: UI components and styling
- **Bootstrap Icons**: Icon library

### Internal Dependencies
- `js/storageManager.js`: Local storage management
- `js/firestore.js`: Firestore operations
- `js/auth.js`: Authentication system
- `js/common.js`: Common utilities

## 📱 User Interface

### Main Features
- **Deck Grid**: Visual overview of all flashcard decks
- **Study Modal**: Interactive flashcard study interface
- **Card Management**: Add, edit, and delete flashcards
- **Progress Tracking**: Visual progress indicators

### Responsive Design
- Mobile-friendly interface
- Touch-friendly controls
- Adaptive layouts for different screen sizes

## 🧪 Testing

### Manual Testing
1. Create a new deck
2. Add several flashcards
3. Start a study session
4. Rate cards with different scores
5. Verify SM-2 algorithm scheduling

### Integration Testing
1. Connect flashcards to tasks
2. Test workspace integration
3. Verify cross-tab synchronization
4. Test Firebase data persistence

## 🔧 Configuration

### Firebase Setup
Ensure Firebase is properly configured with:
- Firestore database
- Authentication enabled
- Proper security rules

### SM-2 Algorithm Parameters
- Initial ease factor: 2.5
- Minimum ease factor: 1.3
- Quality threshold: 3 (for successful recall)

## 🐛 Known Issues

1. **Path Dependencies**: Some import paths may need adjustment after modularization
2. **Firebase Initialization**: Ensure proper Firebase setup before using cloud features
3. **Cross-tab Sync**: May require page refresh in some browsers

## 🚀 Future Enhancements

- [ ] Image support in flashcards
- [ ] Audio pronunciation features
- [ ] Advanced statistics and analytics
- [ ] Collaborative deck sharing
- [ ] Import/export functionality
- [ ] Mobile app integration

## 📄 License

Part of the GPAce application suite.
